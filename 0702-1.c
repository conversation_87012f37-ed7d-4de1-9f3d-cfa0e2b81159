#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// =================================================================
// 1. グローバル変数の定義 (#define を使用)
//    これらの値を変更することで、計算条件を自動的に変更できます。
// =================================================================
#define N  50       // N: フーリエ級数の打ち切り波数
#define NT 1000     // NT: 時間の分割数
#define NX 256      // NX: 空間の分割数
#define T  6.283185 // T: シミュレーションの総時間 (2*PI)
#define C  1.0      // C: 移流速度


// =================================================================
// main関数
// =================================================================
int main(void) {
    // ======== 2. ローカル変数の定義 ========
    // 配列は1次元配列のみ使用
    double ak[N + 1], bk[N + 1];
    double ak_new[N + 1], bk_new[N + 1];
    double u[NX];
    double x[NX];
    double c0;

    // 時間と空間の刻み幅
    double dt = T / NT;
    double dx = (2.0 * M_PI) / NX;

    // ======== 初期係数の計算 ========
    double exp_factor = exp(2.0 * M_PI) - 1.0;
    c0 = exp_factor / M_PI;

    for (int k = 1; k <= N; k++) {
        ak[k] = exp_factor / (M_PI * (1.0 + k * k));
        bk[k] = -k * exp_factor / (M_PI * (1.0 + k * k));
    }
    
    // 空間グリッドの作成
    for (int j = 0; j < NX; j++) {
        x[j] = j * dx;
    }

    printf("シミュレーションを開始します。\n");
    printf("パラメータ: C=%.1f, N=%d, NT=%d, NX=%d, T=%.2f\n", C, N, NT, NX, T);
    
    // ======== 時間発展ループ ========
    // 注意事項に従い、1からNTまでループし、NT個のファイルを出力します。
    for (int n = 1; n <= NT; n++) {
        double current_time = n * dt;

        // --- オイラー法による係数の更新 ---
        for (int k = 1; k <= N; k++) {
            ak_new[k] = ak[k] + dt * (C * k * bk[k]);
            bk_new[k] = bk[k] - dt * (C * k * ak[k]);
        }
        // 全ての係数を計算してから更新を適用
        for (int k = 1; k <= N; k++) {
            ak[k] = ak_new[k];
            bk[k] = bk_new[k];
        }

        // --- u(x,t) の再構成 ---
        for (int j = 0; j < NX; j++) {
            u[j] = c0 / 2.0; // 定数項
            for (int k = 1; k <= N; k++) {
                u[j] += ak[k] * cos(k * x[j]) + bk[k] * sin(k * x[j]);
            }
        }

        // ======== 3. 計算結果のファイル出力 (.txt) ========
        char txt_filename[256];
        sprintf(txt_filename, "data/%d.txt", n); // ファイル名を連番で生成
        
        FILE *fp_txt = fopen(txt_filename, "w");
        if (fp_txt == NULL) {
            printf("Error: dataフォルダが開けません。\n");
            return 1;
        }
        for (int j = 0; j < NX; j++) {
            fprintf(fp_txt, "%.8f %.8f\n", x[j], u[j]);
        }
        fclose(fp_txt);

        // ======== 4. gnuplotによる可視化 (.png) ========
        char png_filename[256];
        sprintf(png_filename, "anim/%d.png", n); // .txtと同じ連番でPNGファイル名生成

        FILE *gp = popen("gnuplot", "w");
        if (gp == NULL) {
            printf("Error: gnuplotを起動できません。\n");
            return 1;
        }

        fprintf(gp, "set terminal pngcairo size 800,600\n");
        fprintf(gp, "set output '%s'\n", png_filename);
        fprintf(gp, "set title '1D Advection Equation (t = %.3f)'\n", current_time);
        fprintf(gp, "set xlabel 'x'\n");
        fprintf(gp, "set ylabel 'u(x,t)'\n");
        fprintf(gp, "set xrange [0:%.8f]\n", 2.0 * M_PI);
        fprintf(gp, "set yrange [-20:150]\n"); // グラフ範囲を固定し見やすくする
        fprintf(gp, "set grid\n");
        fprintf(gp, "plot '%s' with lines title 'u(x,t)'\n", txt_filename);
        
        pclose(gp);

        if (n % (NT / 10) == 0) { // 10%進むごとに進捗を表示
            printf("進捗: %d%% (%d/%d)\n", (n * 100) / NT, n, NT);
        }
    }

    printf("シミュレーションが完了しました。\n");
    return 0;
}